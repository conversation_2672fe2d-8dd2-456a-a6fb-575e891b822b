{"version": 3, "file": "codec.js", "sourceRoot": "", "sources": ["../../src/json-binary/codec.ts"], "names": [], "mappings": ";;;AAAA,wCAA4D;AAC5D,mEAA8D;AAC9D,+DAA0D;AAC1D,6EAAwE;AACxE,2CAA0E;AAG1E,MAAM,iBAAiB,GAAG,uBAAW,CAAC,MAAM,CAAC;AAC7C,MAAM,qBAAqB,GAAG,2BAAe,CAAC,MAAM,CAAC;AACrD,MAAM,qBAAqB,GAAG,2BAAe,CAAC,MAAM,CAAC;AACrD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;AAEtE,MAAM,eAAe,GAAG,CAAC,GAAW,EAAqB,EAAE;IACzD,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;IAC3C,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,UAAU,KAAK,CAAC,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC/D,MAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IAChD,MAAM,GAAG,GAAG,IAAA,uBAAU,EAAC,GAAG,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IACtD,OAAO,IAAI,2BAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC;AACxD,CAAC,CAAC;AAKK,MAAM,YAAY,GAAG,CAAC,KAAc,EAAW,EAAE;IACtD,IAAI,CAAC,KAAK;QAAE,OAAO,KAAK,CAAC;IACzB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,QAAQ,OAAO,IAAI,EAAE,CAAC;gBACpB,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACd,IAAA,oBAAY,EAAC,IAAI,CAAC,CAAC;oBACnB,SAAS;gBACX,CAAC;gBACD,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACd,IAAI,IAAI,CAAC,MAAM,GAAG,UAAU;wBAAE,SAAS;oBACvC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK,uBAAW;wBACtD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAA,uBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;yBACtD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB,CAAC,KAAK,2BAAe;wBACnE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,uBAAa,CAAC,IAAA,uBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;yBAC7E,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB,CAAC,KAAK,2BAAe;wBAAE,KAAK,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC1G,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YACxB,MAAM,IAAI,GAAI,KAAa,CAAC,GAAG,CAAC,CAAC;YACjC,QAAQ,OAAO,IAAI,EAAE,CAAC;gBACpB,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACd,IAAA,oBAAY,EAAC,IAAI,CAAC,CAAC;oBACnB,SAAS;gBACX,CAAC;gBACD,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACd,IAAI,IAAI,CAAC,MAAM,GAAG,UAAU;wBAAE,SAAS;oBACvC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK,uBAAW,EAAE,CAAC;wBACzD,MAAM,GAAG,GAAG,IAAA,uBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;wBACzD,KAAa,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;oBAC5B,CAAC;yBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB,CAAC,KAAK,2BAAe,EAAE,CAAC;wBACvE,KAAa,CAAC,GAAG,CAAC,GAAG,IAAI,uBAAa,CAAC,IAAA,uBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;oBAC7F,CAAC;yBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB,CAAC,KAAK,2BAAe;wBACpE,KAAa,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,UAAU;YAAE,OAAO,KAAK,CAAC;QAC5C,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK,uBAAW;YAAE,OAAO,IAAA,uBAAU,EAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACjH,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB,CAAC,KAAK,2BAAe;YAC/D,OAAO,IAAI,uBAAa,CAAC,IAAA,uBAAU,EAAC,KAAK,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC/E,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB,CAAC,KAAK,2BAAe;YAAE,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC;;YAC5F,OAAO,KAAK,CAAC;IACpB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAtDW,QAAA,YAAY,gBAsDvB;AAEK,MAAM,KAAK,GAAG,CAAC,IAAY,EAAW,EAAE;IAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAChC,OAAO,IAAA,oBAAY,EAAC,MAAM,CAAC,CAAC;AAC9B,CAAC,CAAC;AAHW,QAAA,KAAK,SAGhB;AAEK,MAAM,eAAe,GAAG,CAAuB,KAAQ,EAAoB,EAAE,CAChE,CAAC,uBAAW,GAAG,IAAA,mBAAQ,EAAC,KAAK,CAAC,CAAC,CAAC;AADvC,QAAA,eAAe,mBACwB;AAM7C,MAAM,UAAU,GAAG,CAAC,KAAc,EAAW,EAAE;IACpD,IAAI,CAAC,KAAK;QAAE,OAAO,KAAK,CAAC;IACzB,IAAI,IAAA,2BAAY,EAAC,KAAK,CAAC;QAAE,OAAO,IAAA,uBAAe,EAAC,KAAK,CAAC,CAAC;IACvD,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;QAC3B,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,kBAAU,EAAC,IAAI,CAAC,CAAC,CAAC;QACxE,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IACD,IAAI,KAAK,YAAY,uBAAa;QAAE,OAAO,2BAAe,GAAG,IAAA,mBAAQ,EAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACjF,IAAI,KAAK,YAAY,2BAAiB;QAAE,OAAO,2BAAe,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAA,mBAAQ,EAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACvG,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,MAAM,GAAG,GAA6B,EAAE,CAAC;QACzC,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YACxB,MAAM,IAAI,GAAI,KAAa,CAAC,GAAG,CAAC,CAAC;YACjC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,kBAAU,EAAC,IAAI,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAvBW,QAAA,UAAU,cAuBrB;AAMK,MAAM,SAAS,GAAc,CAAC,KAAc,EAAE,QAAa,EAAE,KAAU,EAAE,EAAE;IAChF,MAAM,OAAO,GAAG,IAAA,kBAAU,EAAC,KAAK,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAClD,CAAC,CAAC;AAHW,QAAA,SAAS,aAGpB"}