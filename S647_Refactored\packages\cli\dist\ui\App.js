import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useEffect } from 'react';
import { Box, Text, useApp } from 'ink';
import { ChatScreen } from './screens/ChatScreen.js';
import { ConfigScreen } from './screens/ConfigScreen.js';
import { ProvidersScreen } from './screens/ProvidersScreen.js';
import { ToolsScreen } from './screens/ToolsScreen.js';
import { ErrorBoundary } from './components/ErrorBoundary.js';
import { LoadingSpinner } from './components/LoadingSpinner.js';
import { StatusBar } from './components/StatusBar.js';
import { AIManager } from '@s647/core';
/**
 * Main App component
 */
export function App({ args, config, logger }) {
    const { exit } = useApp();
    const [state, setState] = useState({
        currentScreen: 'chat',
        isLoading: true,
    });
    // Initialize AI Manager
    useEffect(() => {
        const initializeAI = async () => {
            try {
                const aiManager = new AIManager(logger);
                const result = await aiManager.initialize(config.providers, config.defaultProvider);
                if (!result.success) {
                    throw result.error;
                }
                setState(prev => ({
                    ...prev,
                    aiManager,
                    isLoading: false,
                }));
                logger.info('AI Manager initialized successfully');
            }
            catch (error) {
                const err = error instanceof Error ? error : new Error(String(error));
                logger.error('Failed to initialize AI Manager', { error: err.message });
                setState(prev => ({
                    ...prev,
                    error: err,
                    isLoading: false,
                }));
            }
        };
        initializeAI();
    }, [config, logger]);
    // Handle keyboard shortcuts
    useEffect(() => {
        const handleInput = (data) => {
            const key = data.toString();
            // Ctrl+C to exit
            if (key === '\u0003') {
                exit();
                return;
            }
            // Function keys for screen navigation
            switch (key) {
                case '\u001b[15~': // F5 - Chat
                    setState(prev => ({ ...prev, currentScreen: 'chat' }));
                    break;
                case '\u001b[17~': // F6 - Config
                    setState(prev => ({ ...prev, currentScreen: 'config' }));
                    break;
                case '\u001b[18~': // F7 - Providers
                    setState(prev => ({ ...prev, currentScreen: 'providers' }));
                    break;
                case '\u001b[19~': // F8 - Tools
                    setState(prev => ({ ...prev, currentScreen: 'tools' }));
                    break;
            }
        };
        process.stdin.on('data', handleInput);
        return () => {
            process.stdin.off('data', handleInput);
        };
    }, [exit]);
    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (state.aiManager) {
                state.aiManager.dispose();
            }
        };
    }, [state.aiManager]);
    // Handle navigation
    const navigateToScreen = (screen) => {
        setState(prev => ({ ...prev, currentScreen: screen }));
    };
    // Render loading state
    if (state.isLoading) {
        return (_jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsx(Box, { flexGrow: 1, justifyContent: "center", alignItems: "center", children: _jsx(LoadingSpinner, { text: "Initializing S647..." }) }), _jsx(StatusBar, { status: "loading", message: "Setting up AI providers..." })] }));
    }
    // Render error state
    if (state.error) {
        return (_jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsx(Box, { flexGrow: 1, justifyContent: "center", alignItems: "center", children: _jsxs(Box, { flexDirection: "column", alignItems: "center", children: [_jsx(Text, { color: "red", bold: true, children: "\u274C Failed to initialize S647" }), _jsx(Text, { color: "red", children: state.error.message }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", children: "Press Ctrl+C to exit" }) })] }) }), _jsx(StatusBar, { status: "error", message: state.error.message })] }));
    }
    // Render main app
    return (_jsx(ErrorBoundary, { children: _jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsxs(Box, { flexGrow: 1, children: [state.currentScreen === 'chat' && (_jsx(ChatScreen, { aiManager: state.aiManager, config: config, logger: logger, onNavigate: navigateToScreen })), state.currentScreen === 'config' && (_jsx(ConfigScreen, { config: config, logger: logger, onNavigate: navigateToScreen })), state.currentScreen === 'providers' && (_jsx(ProvidersScreen, { aiManager: state.aiManager, config: config, logger: logger, onNavigate: navigateToScreen })), state.currentScreen === 'tools' && (_jsx(ToolsScreen, { config: config, logger: logger, onNavigate: navigateToScreen }))] }), _jsx(StatusBar, { status: "ready", message: `Screen: ${state.currentScreen} | F5: Chat | F6: Config | F7: Providers | F8: Tools | Ctrl+C: Exit` })] }) }));
}
//# sourceMappingURL=App.js.map