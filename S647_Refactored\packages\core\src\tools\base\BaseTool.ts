/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  Tool,
  ToolId,
  ToolParams,
  ToolContext,
  ToolResult,
  ToolHelp,
  AsyncResult,
  JsonValue,
} from '@s647/shared';
import { z } from 'zod';

/**
 * Base implementation for all tools
 */
export abstract class BaseTool implements Tool {
  public readonly id: ToolId;
  public readonly name: string;
  public readonly description: string;
  public readonly version: string;
  public readonly category: string;
  public readonly schema: JsonValue;

  protected readonly parameterSchema: z.ZodSchema;

  constructor(
    id: ToolId,
    name: string,
    description: string,
    version: string,
    category: string,
    parameterSchema: z.ZodSchema,
    schema?: JsonValue
  ) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.version = version;
    this.category = category;
    this.parameterSchema = parameterSchema;
    this.schema = schema || this.generateSchema();
  }

  /**
   * Execute the tool with given parameters
   */
  public abstract execute(params: ToolParams, context: ToolContext): AsyncResult<ToolResult>;

  /**
   * Validate tool parameters
   */
  public validate(params: ToolParams): { valid: boolean; errors: string[] } {
    try {
      this.parameterSchema.parse(params);
      return { valid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        };
      }
      return {
        valid: false,
        errors: [error instanceof Error ? error.message : 'Unknown validation error']
      };
    }
  }

  /**
   * Get help information for the tool
   */
  public abstract getHelp(): ToolHelp;

  /**
   * Generate JSON schema from Zod schema
   */
  protected generateSchema(): JsonValue {
    // Basic schema generation - can be enhanced with zod-to-json-schema
    return {
      type: 'object',
      properties: {},
      required: []
    };
  }

  /**
   * Handle errors consistently
   */
  protected handleError(error: unknown): Error {
    if (error instanceof Error) {
      return error;
    }
    return new Error(String(error));
  }

  /**
   * Create a successful result
   */
  protected success<T>(data: T): AsyncResult<T> {
    return Promise.resolve({ success: true, data });
  }

  /**
   * Create an error result
   */
  protected error(error: Error | string): AsyncResult<never> {
    const err = typeof error === 'string' ? new Error(error) : error;
    return Promise.resolve({ success: false, error: err });
  }

  /**
   * Validate and parse parameters
   */
  protected async validateAndParse<T>(params: ToolParams): Promise<T> {
    const validation = this.validate(params);
    if (!validation.valid) {
      throw new Error(`Parameter validation failed: ${validation.errors.join(', ')}`);
    }
    return this.parameterSchema.parse(params) as T;
  }
}
