/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { MistralProviderConfig, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, AsyncResult, ChatMessage, ProviderId } from '@s647/shared';
import { BaseProvider } from '../interfaces/provider.js';
/**
 * Mistral provider implementation
 */
export declare class MistralProvider extends BaseProvider {
    private client;
    constructor(id: ProviderId, config: MistralProviderConfig);
    get mistralConfig(): MistralProviderConfig;
    /**
     * Initialize the Mistral provider
     */
    initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Get available models
     */
    getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion
     */
    createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion
     */
    createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings (supported by Mistral)
     */
    createEmbedding(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[]): AsyncResult<number>;
    /**
     * Convert tools to Mistral format
     */
    private convertTools;
    /**
     * Convert finish reason to OpenAI format
     */
    private convertFinishReason;
    /**
     * Convert Mistral stream to OpenAI format
     */
    private convertStream;
}
//# sourceMappingURL=mistral.d.ts.map