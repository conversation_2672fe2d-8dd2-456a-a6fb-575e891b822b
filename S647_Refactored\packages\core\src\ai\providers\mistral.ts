/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type {
  MistralProviderConfig,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelInfo,
  AsyncResult,
  ChatMessage,
  ProviderId,
} from '@s647/shared';
import { BaseProvider } from '../interfaces/provider.js';

/**
 * Mistral provider implementation
 */
export class MistralProvider extends BaseProvider {
  private client: any; // Mistral client instance

  constructor(id: ProviderId, config: MistralProviderConfig) {
    super(id, config);
  }

  public get mistralConfig(): MistralProviderConfig {
    return this.config as MistralProviderConfig;
  }

  /**
   * Initialize the Mistral provider
   */
  public async initialize(): AsyncResult<void> {
    try {
      this.validateConfig();

      // Initialize Mistral client
      const { Mistral } = await import('@mistralai/mistralai');

      if (!this.mistralConfig.apiKey) {
        throw new Error('Mistral API key is required');
      }

      this.client = new Mistral({
        apiKey: this.mistralConfig.apiKey,
        ...(this.mistralConfig.baseUrl && { serverURL: this.mistralConfig.baseUrl }),
      });

      // Test the connection
      await this.isAvailable();
      this.setStatus('available');

      return { success: true, data: undefined };
    } catch (error) {
      this.setStatus('error');
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Check if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      if (!this.client) {
        return false;
      }

      // Test with a simple chat completion
      await this.client.chat.complete({
        model: 'mistral-small-latest',
        messages: [{ role: 'user', content: 'Hi' }],
        maxTokens: 1,
      });

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get available models
   */
  public async getModels(): AsyncResult<ModelInfo[]> {
    try {
      // Mistral doesn't have a models endpoint, so we return known models
      const models: ModelInfo[] = [
        {
          id: 'mistral-large-latest',
          name: 'Mistral Large',
          provider: 'mistral' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: false,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: false,
          },
          contextLength: 128000,
          maxTokens: 8192,
        },
        {
          id: 'mistral-medium-latest',
          name: 'Mistral Medium',
          provider: 'mistral' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: false,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: false,
          },
          contextLength: 32000,
          maxTokens: 8192,
        },
        {
          id: 'mistral-small-latest',
          name: 'Mistral Small',
          provider: 'mistral' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: false,
            functionCalling: true,
            streaming: true,
            systemMessages: true,
            multiModal: false,
          },
          contextLength: 32000,
          maxTokens: 8192,
        },
        {
          id: 'open-mistral-nemo',
          name: 'Mistral Nemo',
          provider: 'mistral' as const,
          capabilities: {
            chat: true,
            completion: true,
            embedding: false,
            vision: false,
            functionCalling: false,
            streaming: true,
            systemMessages: true,
            multiModal: false,
          },
          contextLength: 128000,
          maxTokens: 8192,
        },
      ];

      return { success: true, data: models };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a chat completion
   */
  public async createChatCompletion(
    request: ChatCompletionRequest
  ): AsyncResult<ChatCompletionResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.chat.complete({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        maxTokens: request.maxTokens,
        topP: request.topP,
        stop: Array.isArray(request.stop) ? request.stop : request.stop ? [request.stop] : undefined,
        stream: false,
        tools: request.tools ? this.convertTools(request.tools) : undefined,
        toolChoice: request.toolChoice,
      });

      // Convert response to OpenAI format
      const convertedResponse: ChatCompletionResponse = {
        id: response.id || `chatcmpl-${Date.now()}`,
        object: 'chat.completion',
        created: response.created || Date.now(),
        model: request.model,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response.choices[0]?.message?.content || '',
            ...(response.choices[0]?.message?.toolCalls && {
              tool_calls: response.choices[0].message.toolCalls
            }),
          },
          finishReason: this.convertFinishReason(response.choices[0]?.finishReason),
        }],
        usage: {
          promptTokens: response.usage?.promptTokens || 0,
          completionTokens: response.usage?.completionTokens || 0,
          totalTokens: response.usage?.totalTokens || 0,
        },
      };

      return { success: true, data: convertedResponse };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create a streaming chat completion
   */
  public async createChatCompletionStream(
    request: ChatCompletionRequest
  ): AsyncResult<AsyncIterable<ChatCompletionChunk>> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const stream = await this.client.chat.stream({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        maxTokens: request.maxTokens,
        topP: request.topP,
        stop: Array.isArray(request.stop) ? request.stop : request.stop ? [request.stop] : undefined,
        tools: request.tools ? this.convertTools(request.tools) : undefined,
        toolChoice: request.toolChoice,
      });

      // Convert stream to OpenAI format
      const convertedStream = this.convertStream(stream, request.model);

      return { success: true, data: convertedStream };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Create embeddings (supported by Mistral)
   */
  public async createEmbedding(
    request: EmbeddingRequest
  ): AsyncResult<EmbeddingResponse> {
    try {
      if (!this.client) {
        throw new Error('Provider not initialized');
      }

      const response = await this.client.embeddings.create({
        model: request.model || 'mistral-embed',
        input: Array.isArray(request.input) ? request.input : [request.input],
      });

      return { success: true, data: response };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Count tokens for a given input
   */
  public async countTokens(
    input: string | ChatMessage[]
  ): AsyncResult<number> {
    try {
      // Mistral doesn't have a dedicated token counting endpoint
      // Use estimation based on character count
      const text = typeof input === 'string'
        ? input
        : input.map(msg => typeof msg.content === 'string' ? msg.content : '').join(' ');

      // Mistral uses roughly 4 characters per token
      const estimatedTokens = Math.ceil(text.length / 4);
      return { success: true, data: estimatedTokens };
    } catch (error) {
      return { success: false, error: this.handleError(error) };
    }
  }

  /**
   * Convert tools to Mistral format
   */
  private convertTools(tools: any[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.function.name,
        description: tool.function.description,
        parameters: tool.function.parameters,
      },
    }));
  }

  /**
   * Convert finish reason to OpenAI format
   */
  private convertFinishReason(finishReason: string | undefined): 'stop' | 'length' | 'function_call' | 'tool_calls' | 'content_filter' {
    switch (finishReason) {
      case 'stop':
        return 'stop';
      case 'length':
        return 'length';
      case 'tool_calls':
        return 'tool_calls';
      case 'model_length':
        return 'length';
      default:
        return 'stop';
    }
  }

  /**
   * Convert Mistral stream to OpenAI format
   */
  private async* convertStream(stream: any, model: string): AsyncIterable<ChatCompletionChunk> {
    const messageId = `chatcmpl-${Date.now()}`;

    for await (const chunk of stream) {
      if (chunk.data?.choices?.[0]?.delta?.content) {
        yield {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Date.now(),
          model,
          choices: [{
            index: 0,
            delta: {
              role: 'assistant',
              content: chunk.data.choices[0].delta.content,
            },
          }],
        };
      }

      // Handle tool calls
      if (chunk.data?.choices?.[0]?.delta?.toolCalls) {
        yield {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Date.now(),
          model,
          choices: [{
            index: 0,
            delta: {
              role: 'assistant',
              toolCalls: chunk.data.choices[0].delta.toolCalls,
            },
          }],
        };
      }

      // Handle final chunk
      if (chunk.data?.choices?.[0]?.finishReason) {
        yield {
          id: messageId,
          object: 'chat.completion.chunk',
          created: Date.now(),
          model,
          choices: [{
            index: 0,
            delta: {},
            finishReason: this.convertFinishReason(chunk.data.choices[0].finishReason),
          }],
        };
      }
    }
  }
}
