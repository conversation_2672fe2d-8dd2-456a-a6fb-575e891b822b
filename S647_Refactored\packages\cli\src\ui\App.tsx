/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useApp } from 'ink';
import type { CliArgs } from '../config/args.js';
import type { Configuration } from '../config/types.js';
import type { Logger } from '@s647/shared';
import { ChatScreen } from './screens/ChatScreen.js';
import { ConfigScreen } from './screens/ConfigScreen.js';
import { ProvidersScreen } from './screens/ProvidersScreen.js';
import { ToolsScreen } from './screens/ToolsScreen.js';
import { ErrorBoundary } from './components/ErrorBoundary.js';
import { LoadingSpinner } from './components/LoadingSpinner.js';
import { StatusBar } from './components/StatusBar.js';
import { AIManager } from '@s647/core';

/**
 * Screen types
 */
export type Screen = 'chat' | 'config' | 'providers' | 'tools';

/**
 * App props
 */
export interface AppProps {
  args: CliArgs;
  config: Configuration;
  logger: Logger;
}

/**
 * App state
 */
interface AppState {
  currentScreen: Screen;
  aiManager?: AIManager;
  isLoading: boolean;
  error?: Error;
}

/**
 * Main App component
 */
export function App({ args, config, logger }: AppProps): React.JSX.Element {
  const { exit } = useApp();
  const [state, setState] = useState<AppState>({
    currentScreen: 'chat',
    isLoading: true,
  });

  // Initialize AI Manager
  useEffect(() => {
    const initializeAI = async () => {
      try {
        const aiManager = new AIManager(logger);
        const result = await aiManager.initialize(
          config.providers,
          config.defaultProvider
        );

        if (!result.success) {
          throw result.error;
        }

        setState(prev => ({
          ...prev,
          aiManager,
          isLoading: false,
        }));

        logger.info('AI Manager initialized successfully');
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        logger.error('Failed to initialize AI Manager', { error: err.message });
        
        setState(prev => ({
          ...prev,
          error: err,
          isLoading: false,
        }));
      }
    };

    initializeAI();
  }, [config, logger]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleInput = (data: Buffer) => {
      const key = data.toString();
      
      // Ctrl+C to exit
      if (key === '\u0003') {
        exit();
        return;
      }
      
      // Function keys for screen navigation
      switch (key) {
        case '\u001b[15~': // F5 - Chat
          setState(prev => ({ ...prev, currentScreen: 'chat' }));
          break;
        case '\u001b[17~': // F6 - Config
          setState(prev => ({ ...prev, currentScreen: 'config' }));
          break;
        case '\u001b[18~': // F7 - Providers
          setState(prev => ({ ...prev, currentScreen: 'providers' }));
          break;
        case '\u001b[19~': // F8 - Tools
          setState(prev => ({ ...prev, currentScreen: 'tools' }));
          break;
      }
    };

    process.stdin.on('data', handleInput);
    return () => {
      process.stdin.off('data', handleInput);
    };
  }, [exit]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (state.aiManager) {
        state.aiManager.dispose();
      }
    };
  }, [state.aiManager]);

  // Handle navigation
  const navigateToScreen = (screen: Screen) => {
    setState(prev => ({ ...prev, currentScreen: screen }));
  };

  // Render loading state
  if (state.isLoading) {
    return (
      <Box flexDirection="column" height="100%">
        <Box flexGrow={1} justifyContent="center" alignItems="center">
          <LoadingSpinner text="Initializing S647..." />
        </Box>
        <StatusBar 
          status="loading" 
          message="Setting up AI providers..." 
        />
      </Box>
    );
  }

  // Render error state
  if (state.error) {
    return (
      <Box flexDirection="column" height="100%">
        <Box flexGrow={1} justifyContent="center" alignItems="center">
          <Box flexDirection="column" alignItems="center">
            <Text color="red" bold>
              ❌ Failed to initialize S647
            </Text>
            <Text color="red">
              {state.error.message}
            </Text>
            <Box marginTop={1}>
              <Text color="gray">
                Press Ctrl+C to exit
              </Text>
            </Box>
          </Box>
        </Box>
        <StatusBar 
          status="error" 
          message={state.error.message} 
        />
      </Box>
    );
  }

  // Render main app
  return (
    <ErrorBoundary>
      <Box flexDirection="column" height="100%">
        <Box flexGrow={1}>
          {state.currentScreen === 'chat' && (
            <ChatScreen
              aiManager={state.aiManager!}
              config={config}
              logger={logger}
              onNavigate={navigateToScreen}
            />
          )}
          {state.currentScreen === 'config' && (
            <ConfigScreen
              config={config}
              logger={logger}
              onNavigate={navigateToScreen}
            />
          )}
          {state.currentScreen === 'providers' && (
            <ProvidersScreen
              aiManager={state.aiManager!}
              config={config}
              logger={logger}
              onNavigate={navigateToScreen}
            />
          )}
          {state.currentScreen === 'tools' && (
            <ToolsScreen
              config={config}
              logger={logger}
              onNavigate={navigateToScreen}
            />
          )}
        </Box>
        
        <StatusBar 
          status="ready" 
          message={`Screen: ${state.currentScreen} | F5: Chat | F6: Config | F7: Providers | F8: Tools | Ctrl+C: Exit`}
        />
      </Box>
    </ErrorBoundary>
  );
}
