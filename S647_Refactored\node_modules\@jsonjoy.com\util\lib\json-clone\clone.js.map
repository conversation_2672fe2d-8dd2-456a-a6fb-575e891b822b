{"version": 3, "file": "clone.js", "sourceRoot": "", "sources": ["../../src/json-clone/clone.ts"], "names": [], "mappings": ";;;AAAA,MAAM,EAAC,OAAO,EAAC,GAAG,KAAK,CAAC;AACxB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;AAQxB,MAAM,KAAK,GAAG,CAAc,GAAM,EAAK,EAAE;IAC9C,IAAI,CAAC,GAAG;QAAE,OAAO,GAAG,CAAC;IACrB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACjB,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;YAAE,GAAG,CAAC,IAAI,CAAC,IAAA,aAAK,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,OAAO,GAAmB,CAAC;IAC7B,CAAC;SAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QACnC,MAAM,IAAI,GAAG,UAAU,CAAC,GAAI,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,SAAS,GAAQ,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,SAAS,CAAC,GAAG,CAAC,GAAG,IAAA,aAAK,EAAE,GAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAlBW,QAAA,KAAK,SAkBhB"}