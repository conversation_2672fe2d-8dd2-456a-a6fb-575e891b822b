/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { simpleGit } from 'simple-git';
import type { SimpleGit, StatusResult } from 'simple-git';
import { resolve, isAbsolute } from 'path';
import { z } from 'zod';
import type {
  <PERSON>lParams,
  ToolContext,
  ToolResult,
  ToolHelp,
  AsyncResult,
} from '@s647/shared';
import { BaseTool } from '../../base/BaseTool.js';

const GitStatusParams = z.object({
  repository: z.string().optional().describe('Path to git repository (default: current directory)'),
  porcelain: z.boolean().optional().default(false).describe('Use porcelain format for machine-readable output'),
});

type GitStatusParams = z.infer<typeof GitStatusParams>;

/**
 * Tool for getting git repository status
 */
export class GitStatusTool extends BaseTool {
  constructor() {
    super(
      'git-status',
      'Git Status',
      'Get the status of a git repository',
      '1.0.0',
      'git',
      GitStatusParams
    );
  }

  public async execute(
    params: ToolParams,
    context: ToolContext
  ): AsyncResult<ToolResult> {
    try {
      const { repository, porcelain } = await this.validateAndParse<GitStatusParams>(params);

      // Resolve repository path
      const repoPath = this.resolvePath(repository || '.', context.workingDirectory);

      // Initialize git instance
      const git: SimpleGit = simpleGit(repoPath);

      // Check if it's a git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        return this.error(`Not a git repository: ${repoPath}`);
      }

      // Get status
      const status: StatusResult = await git.status();

      // Format output
      const content = porcelain ? this.formatPorcelain(status) : this.formatHuman(status, repoPath);

      return this.success({
        type: 'text',
        content,
        metadata: {
          repository: repoPath,
          branch: status.current,
          ahead: status.ahead,
          behind: status.behind,
          staged: status.staged.length,
          modified: status.modified.length,
          deleted: status.deleted.length,
          created: status.created.length,
          untracked: status.not_added.length,
          conflicted: status.conflicted.length,
          isClean: status.isClean(),
        },
      });
    } catch (error) {
      return this.error(this.handleError(error));
    }
  }

  public getHelp(): ToolHelp {
    return {
      description: 'Get the status of a git repository including staged, modified, and untracked files',
      parameters: {
        repository: {
          type: 'string',
          description: 'Path to git repository (relative or absolute)',
          required: false,
          default: '.',
        },
        porcelain: {
          type: 'boolean',
          description: 'Use porcelain format for machine-readable output',
          required: false,
          default: false,
        },
      },
      examples: [
        {
          description: 'Get status of current repository',
          parameters: {},
        },
        {
          description: 'Get status of specific repository',
          parameters: { repository: './my-project' },
        },
        {
          description: 'Get machine-readable status',
          parameters: { porcelain: true },
        },
      ],
    };
  }

  private resolvePath(path: string, workingDirectory?: string): string {
    if (isAbsolute(path)) {
      return resolve(path);
    }
    return resolve(workingDirectory || process.cwd(), path);
  }

  private formatHuman(status: StatusResult, repoPath: string): string {
    const lines = [`Git Status for ${repoPath}\n`];

    // Branch info
    lines.push(`Branch: ${status.current || 'HEAD (detached)'}`);
    
    if (status.tracking) {
      lines.push(`Tracking: ${status.tracking}`);
    }

    if (status.ahead > 0) {
      lines.push(`Ahead by ${status.ahead} commit(s)`);
    }

    if (status.behind > 0) {
      lines.push(`Behind by ${status.behind} commit(s)`);
    }

    lines.push('');

    // Changes to be committed (staged)
    if (status.staged.length > 0) {
      lines.push('Changes to be committed:');
      for (const file of status.staged) {
        lines.push(`  modified: ${file}`);
      }
      lines.push('');
    }

    // Changes not staged for commit
    const unstaged = [...status.modified, ...status.deleted];
    if (unstaged.length > 0) {
      lines.push('Changes not staged for commit:');
      for (const file of status.modified) {
        lines.push(`  modified: ${file}`);
      }
      for (const file of status.deleted) {
        lines.push(`  deleted:  ${file}`);
      }
      lines.push('');
    }

    // Untracked files
    if (status.not_added.length > 0) {
      lines.push('Untracked files:');
      for (const file of status.not_added) {
        lines.push(`  ${file}`);
      }
      lines.push('');
    }

    // New files
    if (status.created.length > 0) {
      lines.push('New files:');
      for (const file of status.created) {
        lines.push(`  ${file}`);
      }
      lines.push('');
    }

    // Conflicted files
    if (status.conflicted.length > 0) {
      lines.push('Conflicted files:');
      for (const file of status.conflicted) {
        lines.push(`  ${file}`);
      }
      lines.push('');
    }

    // Clean status
    if (status.isClean()) {
      lines.push('Working tree clean');
    }

    return lines.join('\n');
  }

  private formatPorcelain(status: StatusResult): string {
    const lines: string[] = [];

    // Staged files
    for (const file of status.staged) {
      lines.push(`M  ${file}`);
    }

    // Modified files
    for (const file of status.modified) {
      lines.push(` M ${file}`);
    }

    // Deleted files
    for (const file of status.deleted) {
      lines.push(` D ${file}`);
    }

    // New files
    for (const file of status.created) {
      lines.push(`A  ${file}`);
    }

    // Untracked files
    for (const file of status.not_added) {
      lines.push(`?? ${file}`);
    }

    // Conflicted files
    for (const file of status.conflicted) {
      lines.push(`UU ${file}`);
    }

    return lines.join('\n');
  }
}
