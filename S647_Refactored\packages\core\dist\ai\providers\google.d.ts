/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { GoogleProviderConfig, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, AsyncResult, ChatMessage, ProviderId } from '@s647/shared';
import { BaseProvider } from '../interfaces/provider.js';
/**
 * Google Gemini provider implementation
 */
export declare class GoogleProvider extends BaseProvider {
    private client;
    constructor(id: ProviderId, config: GoogleProviderConfig);
    get googleConfig(): GoogleProviderConfig;
    /**
     * Initialize the Google provider
     */
    initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Get available models
     */
    getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion
     */
    createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion
     */
    createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings (not supported by Gemini chat models)
     */
    createEmbedding(_request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[]): AsyncResult<number>;
    /**
     * Convert messages to Google format
     */
    private convertMessages;
    /**
     * Convert tools to Google format
     */
    private convertTools;
    /**
     * Convert finish reason to OpenAI format
     */
    private convertFinishReason;
    /**
     * Convert Google stream to OpenAI format
     */
    private convertStream;
}
//# sourceMappingURL=google.d.ts.map