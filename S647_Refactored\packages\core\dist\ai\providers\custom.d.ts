/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import type { CustomProviderConfig, ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChunk, EmbeddingRequest, EmbeddingResponse, ModelInfo, AsyncResult, ChatMessage, ProviderId } from '@s647/shared';
import { BaseProvider } from '../interfaces/provider.js';
/**
 * Custom provider implementation
 * Supports user-defined endpoints with OpenAI-compatible APIs
 */
export declare class CustomProvider extends BaseProvider {
    private client;
    constructor(id: ProviderId, config: CustomProviderConfig);
    get customConfig(): CustomProviderConfig;
    /**
     * Initialize the Custom provider
     */
    initialize(): AsyncResult<void>;
    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Get available models from custom endpoint
     */
    getModels(): AsyncResult<ModelInfo[]>;
    /**
     * Create a chat completion (OpenAI-compatible)
     */
    createChatCompletion(request: ChatCompletionRequest): AsyncResult<ChatCompletionResponse>;
    /**
     * Create a streaming chat completion (OpenAI-compatible)
     */
    createChatCompletionStream(request: ChatCompletionRequest): AsyncResult<AsyncIterable<ChatCompletionChunk>>;
    /**
     * Create embeddings (OpenAI-compatible)
     */
    createEmbedding(request: EmbeddingRequest): AsyncResult<EmbeddingResponse>;
    /**
     * Count tokens for a given input
     */
    countTokens(input: string | ChatMessage[]): AsyncResult<number>;
}
//# sourceMappingURL=custom.d.ts.map