{"version": 3, "file": "App.js", "sourceRoot": "", "sources": ["../../src/ui/App.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC;AAIxC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AA0BvC;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAY;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC;IAC1B,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAW;QAC3C,aAAa,EAAE,MAAM;QACrB,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC;IAEH,wBAAwB;IACxB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;gBACxC,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,UAAU,CACvC,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,eAAe,CACvB,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpB,MAAM,MAAM,CAAC,KAAK,CAAC;gBACrB,CAAC;gBAED,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,SAAS,EAAE,KAAK;iBACjB,CAAC,CAAC,CAAC;gBAEJ,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtE,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChB,GAAG,IAAI;oBACP,KAAK,EAAE,GAAG;oBACV,SAAS,EAAE,KAAK;iBACjB,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC,CAAC;QAEF,YAAY,EAAE,CAAC;IACjB,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAErB,4BAA4B;IAC5B,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;YACnC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE5B,iBAAiB;YACjB,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;gBACrB,IAAI,EAAE,CAAC;gBACP,OAAO;YACT,CAAC;YAED,sCAAsC;YACtC,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,YAAY,EAAE,YAAY;oBAC7B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;oBACvD,MAAM;gBACR,KAAK,YAAY,EAAE,cAAc;oBAC/B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;oBACzD,MAAM;gBACR,KAAK,YAAY,EAAE,iBAAiB;oBAClC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;oBAC5D,MAAM;gBACR,KAAK,YAAY,EAAE,aAAa;oBAC9B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;oBACxD,MAAM;YACV,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACtC,OAAO,GAAG,EAAE;YACV,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEX,qBAAqB;IACrB,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,GAAG,EAAE;YACV,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACpB,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IAEtB,oBAAoB;IACpB,MAAM,gBAAgB,GAAG,CAAC,MAAc,EAAE,EAAE;QAC1C,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;IAEF,uBAAuB;IACvB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,aACvC,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,YAC3D,KAAC,cAAc,IAAC,IAAI,EAAC,sBAAsB,GAAG,GAC1C,EACN,KAAC,SAAS,IACR,MAAM,EAAC,SAAS,EAChB,OAAO,EAAC,4BAA4B,GACpC,IACE,CACP,CAAC;IACJ,CAAC;IAED,qBAAqB;IACrB,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,aACvC,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,YAC3D,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,aAC7C,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,EAAC,IAAI,uDAEf,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,YACd,KAAK,CAAC,KAAK,CAAC,OAAO,GACf,EACP,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,qCAEX,GACH,IACF,GACF,EACN,KAAC,SAAS,IACR,MAAM,EAAC,OAAO,EACd,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAC5B,IACE,CACP,CAAC;IACJ,CAAC;IAED,kBAAkB;IAClB,OAAO,CACL,KAAC,aAAa,cACZ,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,aACvC,MAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,aACb,KAAK,CAAC,aAAa,KAAK,MAAM,IAAI,CACjC,KAAC,UAAU,IACT,SAAS,EAAE,KAAK,CAAC,SAAU,EAC3B,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,gBAAgB,GAC5B,CACH,EACA,KAAK,CAAC,aAAa,KAAK,QAAQ,IAAI,CACnC,KAAC,YAAY,IACX,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,gBAAgB,GAC5B,CACH,EACA,KAAK,CAAC,aAAa,KAAK,WAAW,IAAI,CACtC,KAAC,eAAe,IACd,SAAS,EAAE,KAAK,CAAC,SAAU,EAC3B,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,gBAAgB,GAC5B,CACH,EACA,KAAK,CAAC,aAAa,KAAK,OAAO,IAAI,CAClC,KAAC,WAAW,IACV,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,gBAAgB,GAC5B,CACH,IACG,EAEN,KAAC,SAAS,IACR,MAAM,EAAC,OAAO,EACd,OAAO,EAAE,WAAW,KAAK,CAAC,aAAa,qEAAqE,GAC5G,IACE,GACQ,CACjB,CAAC;AACJ,CAAC"}