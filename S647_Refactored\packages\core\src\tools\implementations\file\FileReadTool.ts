/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { promises as fs } from 'fs';
import { join, resolve, isAbsolute } from 'path';
import { z } from 'zod';
import type {
  <PERSON>lParams,
  ToolContext,
  ToolResult,
  <PERSON>l<PERSON>elp,
  AsyncResult,
} from '@s647/shared';
import { BaseTool } from '../../base/BaseTool.js';

const FileReadParams = z.object({
  path: z.string().describe('Path to the file to read'),
  encoding: z.string().optional().default('utf8').describe('File encoding (default: utf8)'),
  maxSize: z.number().optional().default(1024 * 1024).describe('Maximum file size in bytes (default: 1MB)'),
});

type FileReadParams = z.infer<typeof FileReadParams>;

/**
 * Tool for reading file contents
 */
export class FileReadTool extends BaseTool {
  constructor() {
    super(
      'file-read',
      'File Read',
      'Read the contents of a file',
      '1.0.0',
      'file',
      FileReadParams
    );
  }

  public async execute(
    params: ToolParams,
    context: ToolContext
  ): AsyncResult<ToolResult> {
    try {
      const { path, encoding, maxSize } = await this.validateAndParse<FileReadParams>(params);

      // Resolve path relative to working directory
      const resolvedPath = this.resolvePath(path, context.workingDirectory);

      // Check if file exists
      try {
        await fs.access(resolvedPath);
      } catch {
        return this.error(`File not found: ${resolvedPath}`);
      }

      // Check file size
      const stats = await fs.stat(resolvedPath);
      if (stats.size > maxSize) {
        return this.error(`File too large: ${stats.size} bytes (max: ${maxSize} bytes)`);
      }

      // Read file content
      const content = await fs.readFile(resolvedPath, encoding as BufferEncoding);

      return this.success({
        type: 'text',
        content: `File: ${resolvedPath}\nSize: ${stats.size} bytes\n\n${content}`,
        metadata: {
          path: resolvedPath,
          size: stats.size,
          encoding,
          lastModified: stats.mtime.toISOString(),
        },
      });
    } catch (error) {
      return this.error(this.handleError(error));
    }
  }

  public getHelp(): ToolHelp {
    return {
      description: 'Read the contents of a file from the filesystem',
      parameters: {
        path: {
          type: 'string',
          description: 'Path to the file to read (relative or absolute)',
          required: true,
        },
        encoding: {
          type: 'string',
          description: 'File encoding (utf8, ascii, base64, etc.)',
          required: false,
          default: 'utf8',
        },
        maxSize: {
          type: 'number',
          description: 'Maximum file size in bytes',
          required: false,
          default: 1048576,
        },
      },
      examples: [
        {
          description: 'Read a text file',
          parameters: { path: './README.md' },
        },
        {
          description: 'Read a file with specific encoding',
          parameters: { path: './data.txt', encoding: 'ascii' },
        },
        {
          description: 'Read a large file with custom size limit',
          parameters: { path: './large-file.json', maxSize: 5242880 },
        },
      ],
    };
  }

  private resolvePath(path: string, workingDirectory?: string): string {
    if (isAbsolute(path)) {
      return resolve(path);
    }
    return resolve(workingDirectory || process.cwd(), path);
  }
}
